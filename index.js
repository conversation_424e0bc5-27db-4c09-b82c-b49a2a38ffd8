const express = require('express')
const app = express()
const port = process.env.PORT || 3000

// Import routes
const homeRoutes = require('./routes/homeRoutes')
const apiRoutes = require('./routes/apiRoutes')

// Middleware for parsing JSON bodies
app.use(express.json())

// Use routes
app.use('/', homeRoutes)
app.use('/api', apiRoutes)

app.listen(port, () => {
  console.log(`Example app listening on port ${port}`)
})

