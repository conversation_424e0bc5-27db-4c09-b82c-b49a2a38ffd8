const express = require('express')
const app = express()
const port = process.env.PORT || 3000

// Middleware for parsing JSON bodies
app.use(express.json())

// Example GET route that returns a string
app.get('/', (req, res) => {
  res.send('Hello World!')
})

// Example POST route that uses JSON body parsing
app.post('/api/data', (req, res) => {
  console.log('Received JSON body:', req.body)
  res.json({
    message: 'Data received successfully',
    receivedData: req.body
  })
})

app.listen(port, () => {
  console.log(`Example app listening on port ${port}`)
})

